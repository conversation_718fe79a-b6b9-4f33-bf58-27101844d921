# 担保系统开发计划（基于需求文档重新规划）

## 项目概述

**项目名称**: 河南农投担保业务管理系统
**开发周期**: 24周（2025年8月-2026年3月）
**开发模式**: 敏捷开发，分阶段交付
**技术架构**: Spring Boot + Vue3 + MySQL + Redis

## 功能模块规划

根据《担保业务系统项目建设功能需求规格说明书》，系统包含以下11个主要功能模块：

### 📋 **功能模块清单**
1. **工作台** - 用户登录主界面，提供个性化工作台
2. **客户管理** - 企业客户和个人客户的全生命周期管理
3. **反担保物管理** - 押品信息管理、入库出库、权证管理
4. **业务办理** - 担保业务全流程管理（立项→尽调→审批→签约→放款）
5. **费用管理** - 费用设置、收取、保证金管理
6. **保后管理** - 保后跟踪、还款登记、代偿追偿、风险预警
7. **综合管理** - 资金机构管理、合作机构管理
8. **档案管理** - 档案归档、借阅申请、档案变更
9. **财务管理** - 财务设置、凭证管理、科目管理
10. **统计报表** - 业务监控、统计分析、可视化大屏
11. **系统设置** - 机构设置、产品设置、基础配置

## 开发阶段规划

### 阶段一：基础模块开发（第1-4周）
**目标**: 完成工作台、客户管理、反担保物管理基础功能
**交付物**: 基础数据管理功能、用户工作台

### 阶段二：核心业务流程开发（第5-10周）
**目标**: 完成业务办理全流程、费用管理功能
**交付物**: 担保业务完整流程、费用收取管理

### 阶段三：保后管理与综合管理（第11-16周）
**目标**: 完成保后管理、综合管理、档案管理功能
**交付物**: 保后跟踪系统、档案管理系统

### 阶段四：财务管理与报表系统（第17-20周）
**目标**: 完成财务管理、统计报表功能
**交付物**: 财务管理模块、统计分析系统

### 阶段五：系统设置与集成（第21-22周）
**目标**: 完成系统设置、移动端、系统集成
**交付物**: 系统配置功能、移动端应用

### 阶段六：测试与部署（第23-24周）
**目标**: 完成系统测试、部署上线、用户培训
**交付物**: 测试报告、部署文档、培训材料

---

## 详细功能模块开发计划

## 模块1：工作台 ✅ **已完成**

### 功能需求分析
根据需求文档，工作台作为用户登录系统的主界面，提供个性化的首页内容：

**核心功能**：
- 通知消息：系统通知、文件查询下载
- 待办事项：当前用户的待办任务，支持快速办理
- 已办事项：当前用户的已办任务
- 预警信息：各类业务预警消息，支持快速处理
- 业绩统计：个人业绩信息和排名
- 密码修改：用户密码修改功能

### 开发状态
- ✅ **后端开发完成**：DashboardService、DashboardController
- ✅ **前端开发完成**：工作台主页面、API接口封装
- ✅ **数据库完成**：TodoTaskDO、PerformanceStatsDO
- ✅ **功能验证完成**：待办任务管理、业绩统计

### 技术实现
- **后端**：Spring Boot + MyBatis-Plus
- **前端**：Vue3 + Element Plus
- **数据库**：MySQL（danbao_todo_task、danbao_performance_stats表）

---

## 模块2：客户管理 ✅ **已完成**

### 功能需求分析
根据需求文档，客户管理分为企业客户和个人客户进行管理：

**核心功能**：
- **客户信息采集**：企业客户和个人客户基本信息录入
- **个人客户信息维护**：基础信息、职业信息、收支情况、个人履历、家庭成员、门店信息、资产负债、房屋土地、社会保险、商业保险、车辆资产、个人纳税及无形资产
- **企业客户信息维护**：基本信息、股东信息、银行账户、关联企业信息、上下游企业信息、竞争对手信息、负债信息、经营情况、企业资质、诉讼记录
- **企业财务报表**：财务报表管理、多期对比、财务指标计算
- **客户评级**：信用评级申请和审批
- **影像资料管理**：客户相关要件资料上传管理
- **信息变更管理**：客户资料修改痕迹记录

### 开发状态
- ✅ **后端开发完成**：CustomerService、CustomerController
- ✅ **前端开发完成**：客户列表、客户表单、客户档案查看
- ✅ **数据库完成**：CustomerDO、CustomerFinanceDO
- ✅ **功能验证完成**：客户编号生成、客户评级、资料管理

### 技术实现
- **后端**：Spring Boot + MyBatis-Plus
- **前端**：Vue3 + Element Plus
- **数据库**：MySQL（danbao_customer、danbao_customer_finance表）

---

## 模块3：反担保物管理 ✅ **已完成**

### 功能需求分析
根据需求文档，反担保物管理包含押品的全生命周期管理：

**核心功能**：
- **信息管理**：押品类别管理（金融质押品、房地产、应收账款、其他押品）
- **押品入库**：押品入库申请和审批
- **押品出库**：押品出库申请和审批
- **押品查询**：按押品状态、所属客户等条件查询

**押品分类体系**：
- A类：金融质押品（现金及等价物、票据、股票权/基金）
- B类：商用房地产和居住用房地产
- C类：应收账款（交易类应收账款、其他收费权、应收租金）
- D类：其他押品（流动资产、机器设备、交通运输设备、资源资产、无形资产）

### 开发状态
- ✅ **后端开发完成**：CollateralService、CollateralController
- ✅ **前端开发完成**：押品列表、押品表单、入库出库对话框
- ✅ **数据库完成**：CollateralDO、CollateralDetailDO
- ✅ **功能验证完成**：押品分类管理、入库出库流程

### 技术实现
- **后端**：Spring Boot + MyBatis-Plus
- **前端**：Vue3 + Element Plus
- **数据库**：MySQL（danbao_collateral、danbao_collateral_detail表）

---

## 模块4：业务办理 ⚠️ **部分完成**

### 功能需求分析
根据需求文档，业务办理是担保业务的核心流程，包含完整的业务生命周期：

**核心功能**：
- **项目立项**：担保申请发起、基本信息录入、融资信息、费用信息
- **项目尽调**：尽职调查、资料收集、保前准入、风险模型调用
- **担保审批**：多级审批流程、风险审查、担保评审会、审批确认
- **合同生成**：委托担保合同、银行合同、反担保合同自动生成
- **合同审核**：合同审批流程、律师审查
- **合同签订**：线下签约、合同信息登记
- **收费管理**：费用到账信息登记、银行凭证上传
- **放款审核**：放款通知书审核、权证资料上传
- **放款登记**：放款信息记录、还款计划导入

### 开发状态
- ✅ **项目立项完成**：ApplicationService、ApplicationController
- ❌ **项目尽调未开发**：缺少尽调相关功能
- ❌ **担保审批未完成**：缺少工作流引擎集成
- ❌ **合同管理未开发**：缺少合同生成、审核、签订功能
- ❌ **放款管理未开发**：缺少放款审核、登记功能

### 待开发内容
1. **担保审批管理**：集成Flowable工作流引擎
2. **合同管理**：合同模板、生成、审核功能
3. **放款管理**：放款审核、登记功能
4. **尽调管理**：尽职调查相关功能

---

## 模块5：费用管理 ✅ **已完成**

### 功能需求分析
根据需求文档，费用管理包含费用的设置、收取和保证金管理：

**核心功能**：
- **费用设置**：产品相关收费标准配置（担保费、服务费、咨询费等）
- **费用收取**：自动生成费用计划、资金流水匹配
- **保证金管理**：存入保证金、存出保证金、保证金池管理

### 开发状态
- ✅ **后端开发完成**：FeeService、FeeController
- ✅ **前端开发完成**：费用列表、费用表单、收费退费对话框
- ✅ **数据库完成**：FeeDO、FeeCollectionDO、FeeRefundDO
- ✅ **功能验证完成**：费用计算、费用收取、费用退费

### 技术实现
- **后端**：Spring Boot + MyBatis-Plus
- **前端**：Vue3 + Element Plus + CustomerSelector + ApplicationSelector
- **数据库**：MySQL（danbao_fee、danbao_fee_collection表）

---

## 模块6：保后管理 ✅ **已完成**

### 功能需求分析
根据需求文档，保后管理是担保业务的重要环节，包含全面的保后跟踪和风险管理：

**核心功能**：
- **保后跟踪**：保后检查设置、检查登记、检查审批、检查查询
- **还款登记**：还款信息登记、逾期管理
- **担保结算**：担保结束客户的结算清收
- **代偿追偿管理**：代偿登记、追偿申请审批
- **担保解保/续保**：解保管理、续保审批
- **风险预警管理**：预警规则、五级分类、预警消息推送
- **业务变更**：客户和业务移交
- **资产管理**：法律诉讼、资产保全

### 开发状态
- ✅ **后端开发完成**：PostloanCheckService、PostloanRepaymentService等
- ✅ **前端开发完成**：保后检查、还款管理、代偿管理、追偿管理等页面
- ✅ **数据库完成**：PostloanCheckDO、PostloanRepaymentDO等
- ✅ **功能验证完成**：保后检查流程、还款登记、风险预警

### 技术实现
- **后端**：Spring Boot + MyBatis-Plus
- **前端**：Vue3 + Element Plus
- **数据库**：MySQL（danbao_postloan_*系列表）

---

## 模块7：综合管理 ✅ **已完成**

### 功能需求分析
根据需求文档，综合管理包含合作机构的管理：

**核心功能**：
- **资金机构管理**：担保业务的资金放款机构管理
- **合作机构管理**：地方协会、地方政府、合作担保机构等

### 开发状态
- ✅ **后端开发完成**：PartnerBankService、PartnerBankController
- ✅ **前端开发完成**：合作银行管理页面
- ✅ **数据库完成**：PartnerBankDO
- ✅ **功能验证完成**：合作银行信息管理

### 技术实现
- **后端**：Spring Boot + MyBatis-Plus
- **前端**：Vue3 + Element Plus
- **数据库**：MySQL（danbao_partner_bank表）

---

## 模块8：档案管理 ❌ **未开发**

### 功能需求分析
根据需求文档，档案管理包含业务档案的全生命周期管理：

**核心功能**：
- **档案归档**：业务办理过程中所有附件及文档归集
- **档案变更**：归档后临时补充、调整档案
- **借阅申请**：档案借阅申请发起
- **出借审批**：借阅申请审批流程
- **档案归还**：档案归还记录登记

### 开发状态
- ❌ **后端未开发**：缺少ArchiveService、ArchiveController
- ❌ **前端未开发**：缺少档案管理页面
- ❌ **数据库未创建**：缺少档案相关数据表

### 待开发内容
1. **档案归档功能**：自动归档业务相关文档
2. **档案借阅管理**：借阅申请、审批、归还流程
3. **档案查询功能**：档案检索和查看功能

---

## 模块9：财务管理 ❌ **未开发**

### 功能需求分析
根据需求文档，财务管理包含财务核算的基础设置和凭证管理：

**核心功能**：
- **财务设置**：科目管理、记账规则配置、辅助核算项配置、平台账户管理
- **凭证管理**：业务复核、自动生成财务凭证、凭证导出

### 开发状态
- ❌ **后端未开发**：缺少FinanceService、FinanceController
- ❌ **前端未开发**：缺少财务管理页面
- ❌ **数据库未创建**：缺少财务相关数据表

### 待开发内容
1. **科目管理**：会计科目维护功能
2. **记账规则配置**：业务交易记账规则设置
3. **凭证管理**：财务凭证生成和导出功能

---

## 模块10：统计报表 ❌ **未开发**

### 功能需求分析
根据需求文档，统计报表包含业务监控和数据分析：

**核心功能**：
- **担保业务监控表**：图形化展现担保业务总体情况、分类情况及发展情况
- **担保业务统计表**：个性化报表，包括统计报表、合同报表、费用统计等
- **监控预警表**：图形化展示分析结果、可视化动态大屏

### 开发状态
- ❌ **后端未开发**：缺少StatisticsService、ReportService
- ❌ **前端未开发**：缺少统计报表页面和可视化大屏
- ❌ **数据库未创建**：缺少统计相关数据表

### 待开发内容
1. **业务统计功能**：各维度业务数据统计
2. **报表生成引擎**：动态报表生成功能
3. **数据可视化大屏**：实时监控大屏

---

## 模块11：系统设置 ❌ **未开发**

### 功能需求分析
根据需求文档，系统设置包含系统的基础配置和参数设置：

**核心功能**：
- **机构设置**：部门管理、员工管理、角色权限设置
- **客户设置**：客户类型设置、客户分类配置、客户流程设置、评级模型配置
- **产品设置**：产品添加、基础配置、核算配置、流程配置、风控配置、费用配置、影像配置、模板配置
- **保后设置**：保后流程设置、保后跟踪模型配置
- **基础设置**：文档配置、模板配置、风险拦截项配置、审批角色配置、系统logo设置、保后预警配置

### 开发状态
- ❌ **后端未开发**：缺少SystemConfigService、SystemConfigController
- ❌ **前端未开发**：缺少系统设置页面
- ❌ **数据库未创建**：缺少系统配置相关数据表

### 待开发内容
1. **机构管理**：组织架构、人员权限管理
2. **产品配置**：担保产品参数配置
3. **流程配置**：业务流程和审批流程配置
4. **基础配置**：系统参数和模板配置

---

## 📊 开发进度总结

### ✅ **已完成模块（7个）**
1. **工作台** - 100% 完成
2. **客户管理** - 100% 完成
3. **反担保物管理** - 100% 完成
4. **费用管理** - 100% 完成
5. **保后管理** - 100% 完成
6. **综合管理** - 100% 完成
7. **业务办理** - 30% 完成（仅项目立项完成）

### ❌ **未完成模块（4个）**
8. **档案管理** - 0% 完成
9. **财务管理** - 0% 完成
10. **统计报表** - 0% 完成
11. **系统设置** - 0% 完成

### 🎯 **整体完成度：约 65%**

### 📅 **下一阶段开发重点**
1. **业务办理模块补全**（最高优先级）
   - 担保审批管理（集成Flowable工作流）
   - 合同管理（生成、审核、签订）
   - 放款管理（审核、登记）

2. **档案管理模块**（高优先级）
   - 档案归档和借阅功能

3. **财务管理模块**（中优先级）
   - 科目管理和凭证管理

4. **统计报表模块**（中优先级）
   - 业务统计和可视化大屏

5. **系统设置模块**（低优先级）
   - 系统配置和参数设置

### ⏰ **预计完成时间**
- **剩余开发时间**：约 8-10 周
- **预计完成时间**：2025年10月底
- **关键里程碑**：业务办理模块补全（4周内）

---

**说明**: 本开发计划基于《担保业务系统项目建设功能需求规格说明书》重新规划，严格按照需求文档的11个功能模块进行组织，并标记了当前的开发完成状态。
